<template>
  <div class="pc-profile-view">
    <div class="profile-container">
      <!-- 用户信息部分 -->
      <div class="user-info-section">
        <div class="user-info-card">
          <div class="user-avatar">
            <NuxtImg
              :src="userStore.userInfo?.avatar_url || icons.defaultAvatar.value"
              alt="User avatar"
              loading="eager"
            />
          </div>
          <div class="user-details">
            <h2 class="username">{{ userStore.userInfo?.name }}</h2>
            <p class="uid">UID: {{ userStore.userInfo?.uuid?.slice(0, 7) }}</p>
          </div>
          <button class="settings-button" @click="handleSettings"
            >personal settings</button
          >
        </div>
      </div>

      <!-- 内容标签页部分 -->
      <div class="content-section">
        <div class="tabs">
          <button
            class="tab"
            :class="{ active: activeTab === 'history' }"
            @click="handleTabClick('history')"
          >
            History
          </button>
          <button
            class="tab"
            :class="{ active: activeTab === 'like' }"
            @click="handleTabClick('like')"
          >
            Like ({{ userStore.userLikedStories.length }})
          </button>
        </div>

        <!-- 故事内容区域 -->
        <div class="story-grid-container">
          <!-- 历史记录标签页 -->
          <template v-if="activeTab === 'history'">
            <!-- 加载状态：显示骨架屏 -->
            <template v-if="isHistoryLoading">
              <div class="stories-grid-pc">
                <StoryCard
                  v-for="n in 12"
                  :key="`skeleton-history-${n}`"
                  :story="{ id: `skeleton-${n}`, title: '', status: 'normal' }"
                  :is-pc="true"
                  :loading="true"
                />
              </div>
            </template>
            <!-- 空状态 -->
            <template v-else-if="userStore.userPlayedStories.length === 0">
              <div class="no-data">
                <p>No browsing history available yet.</p>
              </div>
            </template>
            <!-- 正常数据显示 -->
            <template v-else>
              <div class="stories-grid-pc">
                <StoryCard
                  v-for="story in userStore.userPlayedStories"
                  :key="story.id || story.uuid"
                  :story="story"
                  :is-pc="true"
                  @click="handleStoryClick"
                  @need-login="showAuthModal"
                  @need-email="showAuthModal"
                />
              </div>
            </template>
          </template>

          <!-- 喜欢标签页 -->
          <template v-else-if="activeTab === 'like'">
            <!-- 加载状态：显示骨架屏 -->
            <template v-if="isLikeLoading">
              <div class="stories-grid-pc">
                <StoryCard
                  v-for="n in 12"
                  :key="`skeleton-like-${n}`"
                  :story="{ id: `skeleton-${n}`, title: '', status: 'normal' }"
                  :is-pc="true"
                  :loading="true"
                />
              </div>
            </template>
            <!-- 空状态 -->
            <template v-else-if="userStore.userLikedStories.length === 0">
              <div class="no-data">
                <p>No Liked history yet.</p>
              </div>
            </template>
            <!-- 正常数据显示 -->
            <template v-else>
              <div class="stories-grid-pc">
                <StoryCard
                  v-for="story in userStore.userLikedStories"
                  :key="story.id"
                  :story="story"
                  :is-pc="true"
                  @click="handleStoryClick"
                  @need-login="showAuthModal"
                  @need-email="showAuthModal"
                />
              </div>
            </template>
          </template>
        </div>
      </div>
    </div>

    <!-- 故事详情模态框 -->
    <StoryDetailModal
      :visible="showStoryDetail"
      :story-id="selectedStoryId"
      @close="showStoryDetail = false"
      @play="handlePlayStory"
      @need-login="showAuthModal"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'

// 图标配置
const { icons } = useCdn()

// 定义Story类型，与StoryCard组件兼容
interface Story {
  id: string
  title: string
  description?: string
  preview_url?: string
  carousel_image_url?: string[]
  badge?: string
  status?: 'normal' | 'preparing' | 'admin_only'
  is_subscribed?: boolean
  is_fav?: boolean
  author?: string
  tags?: string[]
  categories?: string[]
  // 兼容用户故事数据的字段
  uuid?: string
  cover_url?: string
  image_url?: string
}

// 用户状态管理
const userStore = useUserStore()

// 状态
const activeTab = ref('history')
const isHistoryLoading = ref(false)
const isLikeLoading = ref(false)
const showStoryDetail = ref(false) // 故事详情模态框状态
const selectedStoryId = ref('') // 选中的故事ID

// 处理标签点击
const handleTabClick = async (tab: string) => {
  if (activeTab.value === tab) return // 避免重复点击

  activeTab.value = tab

  // 根据标签加载对应数据
  if (tab === 'history' && userStore.userPlayedStories.length === 0) {
    isHistoryLoading.value = true
    try {
      await userStore.getUserPlayedStories()
    } finally {
      isHistoryLoading.value = false
    }
  } else if (tab === 'like' && userStore.userLikedStories.length === 0) {
    isLikeLoading.value = true
    try {
      await userStore.getUserLikedStories()
    } finally {
      isLikeLoading.value = false
    }
  }
}

// 处理故事点击 - 显示弹窗而不是跳转页面
const handleStoryClick = (story: Story) => {
  console.log('Story clicked:', story)
  selectedStoryId.value = story.id || story.uuid || ''
  showStoryDetail.value = true
}

// 处理设置按钮点击
const handleSettings = () => {
  navigateTo('/user/settings')
}

// 处理开始游戏
const handlePlayStory = (actor: { id: string; name: string }) => {
  console.log('🎮 PC端开始游戏:', {
    storyId: selectedStoryId.value,
    actor: actor.name,
    actorId: actor.id,
  })

  // StoryDetailModal已经处理了路由跳转，这里只需要关闭模态框
  showStoryDetail.value = false
}

// 显示登录弹窗
const showAuthModal = () => {
  console.log('需要登录')
  // 这里可以添加显示登录弹窗的逻辑
}

// 初始化时检查是否需要显示加载状态
onMounted(() => {
  // 如果历史记录为空，显示加载状态
  if (userStore.userPlayedStories.length === 0) {
    isHistoryLoading.value = true
  }
})

// 监听用户数据变化，更新加载状态
watch(
  () => userStore.userPlayedStories.length,
  (newLength) => {
    if (newLength > 0) {
      isHistoryLoading.value = false
    }
  },
)

watch(
  () => userStore.userLikedStories.length,
  (newLength) => {
    if (newLength > 0) {
      isLikeLoading.value = false
    }
  },
)
</script>

<style lang="less" scoped>
.pc-profile-view {
  width: 100%;
  min-height: 100%;
  padding: 24px;
}

.profile-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin: 0 auto;
}

.user-info-card {
  display: flex;
  align-items: center;
  background: var(--bg-card, rgba(255, 255, 255, 0.25));
  border-radius: 20px;
  padding: 24px;
  gap: 20px;
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.user-details {
  flex: 1;

  .username {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary, rgba(0, 0, 0, 0.85));
    margin: 0 0 12px 0;
  }

  .uid {
    font-size: 14px;
    color: var(--text-secondary, rgba(0, 0, 0, 0.65));
    margin: 0;
  }
}

.settings-button {
  border-radius: 40px;
  background: transparent;
  color: var(--text-secondary, rgba(0, 0, 0, 0.65));
  border: 1px solid var(--border-color, rgba(0, 0, 0, 0.2));
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: var(--bg-hover, rgba(0, 0, 0, 0.05));
  }
}

.content-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tabs {
  display: flex;
  gap: 24px;

  .tab {
    background: none;
    border: none;
    color: var(--text-secondary, rgba(0, 0, 0, 0.45));
    font-size: 16px;
    font-weight: 500;
    padding: 0 0 8px 0;
    cursor: pointer;
    position: relative;

    &.active {
      color: var(--accent-color, #ca93f2);
      font-weight: 600;

      &::after {
        content: '';
        position: absolute;
        bottom: -16px;
        left: 0;
        right: 0;
        height: 2px;
        background: var(--accent-color, #ca93f2);
      }
    }
  }
}

.story-grid-container {
  padding: 16px 0;
  min-height: 400px;

  .stories-grid-pc {
    display: grid;
    grid-template-columns: repeat(
      auto-fit,
      220px
    ); /* 大屏幕：固定220px宽度，自动计算列数 */
    gap: 20px;
    padding: 0;
    justify-content: center; /* 居中对齐，避免最后一行不满时左对齐 */

    @media (max-width: 1400px) {
      grid-template-columns: repeat(auto-fit, 200px); /* 中等屏幕：固定200px */
      gap: 18px;
    }

    @media (max-width: 1000px) {
      grid-template-columns: repeat(auto-fit, 180px); /* 小屏幕：固定180px */
      gap: 16px;
    }

    @media (max-width: 768px) {
      grid-template-columns: repeat(auto-fit, 160px); /* 移动端：固定160px */
      gap: 14px;
    }

    @media (max-width: 480px) {
      grid-template-columns: repeat(auto-fit, 140px); /* 超小屏幕：固定140px */
      gap: 12px;
    }
  }

  .no-data {
    text-align: center;
    padding: 40px 0;
    grid-column: 1 / -1;

    p {
      color: var(--text-secondary, rgba(255, 255, 255, 0.6));
      font-size: 16px;
      margin: 0;
    }
  }
}

/* 主题适配 */
body.light-theme .user-info-card {
  background: var(--bg-card, rgba(0, 0, 0, 0.05));
}

body.light-theme .user-details .username {
  color: var(--text-primary, rgba(0, 0, 0, 0.85));
}

body.light-theme .user-details .uid {
  color: var(--text-secondary, rgba(0, 0, 0, 0.65));
}

body.light-theme .settings-button {
  color: var(--text-secondary, rgba(0, 0, 0, 0.65));
  border-color: var(--border-color, rgba(0, 0, 0, 0.2));
}

body.light-theme .settings-button:hover {
  background: var(--bg-hover, rgba(0, 0, 0, 0.05));
}

body.light-theme .tab {
  color: var(--text-secondary, rgba(0, 0, 0, 0.45));
}

body.light-theme .no-data p {
  color: var(--text-secondary, rgba(0, 0, 0, 0.6));
}
</style>
